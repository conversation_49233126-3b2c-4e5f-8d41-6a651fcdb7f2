[23:16:38] [INFO] ✅ MongoDB连接成功 (尝试 1/5)
[23:16:38] [INFO] 🔍 查询需要修复的记录...
[23:16:38] [INFO] 🔍 查询不存在contactinfo字段的记录...
[23:16:43] [INFO] 🔍 查询contactinfo字段为null的记录...
[23:16:47] [INFO] 📊 发现需要修复的记录数: 55,000
[23:16:47] [INFO] 📊 其中不存在字段的记录: 55,000
[23:16:47] [INFO] 📊 其中字段为null的记录: 55,000
[23:16:47] [INFO] 📖 创建ID到XML内容的映射...
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 0 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 100,000 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 100,000 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 100,000 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 100,000 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 100,000 条XML记录，找到 0 条需要修复的记录
[23:16:47] [INFO] 📊 已扫描 200,000 条XML记录，找到 0 条需要修复的记录
[23:16:48] [INFO] 📊 已扫描 300,000 条XML记录，找到 0 条需要修复的记录
[23:16:48] [INFO] 📊 已扫描 400,000 条XML记录，找到 0 条需要修复的记录
[23:16:48] [INFO] 📊 已扫描 500,000 条XML记录，找到 0 条需要修复的记录
[23:16:48] [INFO] 📊 已扫描 600,000 条XML记录，找到 0 条需要修复的记录
[23:16:48] [INFO] 📊 已扫描 700,000 条XML记录，找到 0 条需要修复的记录
[23:16:48] [INFO] 📊 已扫描 800,000 条XML记录，找到 0 条需要修复的记录
[23:16:49] [INFO] 📊 已扫描 900,000 条XML记录，找到 0 条需要修复的记录
[23:16:49] [INFO] 📊 已扫描 1,000,000 条XML记录，找到 0 条需要修复的记录
[23:16:49] [INFO] 📊 已扫描 1,100,000 条XML记录，找到 0 条需要修复的记录
[23:16:49] [INFO] 📊 已扫描 1,200,000 条XML记录，找到 0 条需要修复的记录
[23:16:49] [INFO] 📊 已扫描 1,300,000 条XML记录，找到 0 条需要修复的记录
[23:16:49] [INFO] 📊 已扫描 1,400,000 条XML记录，找到 0 条需要修复的记录
[23:16:49] [INFO] 📊 已扫描 1,500,000 条XML记录，找到 0 条需要修复的记录
[23:16:50] [INFO] 📊 已扫描 1,600,000 条XML记录，找到 0 条需要修复的记录
[23:16:50] [INFO] 📊 已扫描 1,700,000 条XML记录，找到 0 条需要修复的记录
[23:16:50] [INFO] 📊 已扫描 1,800,000 条XML记录，找到 0 条需要修复的记录
[23:16:50] [INFO] 📊 已扫描 1,900,000 条XML记录，找到 55,000 条需要修复的记录
[23:16:50] [INFO] 📊 已扫描 2,000,000 条XML记录，找到 55,000 条需要修复的记录
[23:16:50] [INFO] 📊 已扫描 2,100,000 条XML记录，找到 55,000 条需要修复的记录
[23:16:50] [INFO] 📊 已扫描 2,200,000 条XML记录，找到 55,000 条需要修复的记录
[23:16:51] [INFO] ✅ 映射创建完成，共找到 55,000 条记录的XML内容
[23:16:51] [INFO] 🚀 开始修复失败的contactinfo记录
[23:16:51] [INFO] 📊 需要修复的记录总数: 55,000
[23:16:53] [INFO] ✅ 批量更新成功: 1000 条记录
[23:16:55] [INFO] ✅ 批量更新成功: 1000 条记录
[23:16:56] [INFO] ✅ 批量更新成功: 1000 条记录
[23:16:58] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:00] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:00] [INFO] 📊 修复进度: 已处理 5,000/55,000 条记录 (9.1%), 有contactinfo: 161, 无contactinfo: 4,839, 错误: 0, 耗时: 21.6秒
[23:17:01] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:03] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:07] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:10] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:12] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:12] [INFO] 📊 修复进度: 已处理 10,000/55,000 条记录 (18.2%), 有contactinfo: 313, 无contactinfo: 9,687, 错误: 0, 耗时: 33.8秒
[23:17:14] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:16] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:18] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:20] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:21] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:21] [INFO] 📊 修复进度: 已处理 15,000/55,000 条记录 (27.3%), 有contactinfo: 453, 无contactinfo: 14,547, 错误: 0, 耗时: 43.3秒
[23:17:23] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:25] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:26] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:28] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:29] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:29] [INFO] 📊 修复进度: 已处理 20,000/55,000 条记录 (36.4%), 有contactinfo: 595, 无contactinfo: 19,405, 错误: 0, 耗时: 50.7秒
[23:17:31] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:32] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:34] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:36] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:37] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:37] [INFO] 📊 修复进度: 已处理 25,000/55,000 条记录 (45.5%), 有contactinfo: 719, 无contactinfo: 24,281, 错误: 0, 耗时: 59.2秒
[23:17:39] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:40] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:42] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:43] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:45] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:45] [INFO] 📊 修复进度: 已处理 30,000/55,000 条记录 (54.5%), 有contactinfo: 856, 无contactinfo: 29,144, 错误: 0, 耗时: 67.0秒
[23:17:46] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:48] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:50] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:51] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:52] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:52] [INFO] 📊 修复进度: 已处理 35,000/55,000 条记录 (63.6%), 有contactinfo: 998, 无contactinfo: 34,002, 错误: 0, 耗时: 74.3秒
[23:17:54] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:55] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:56] [INFO] ✅ 批量更新成功: 1000 条记录
[23:17:58] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:00] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:00] [INFO] 📊 修复进度: 已处理 40,000/55,000 条记录 (72.7%), 有contactinfo: 1,140, 无contactinfo: 38,860, 错误: 0, 耗时: 81.6秒
[23:18:02] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:04] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:05] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:07] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:09] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:09] [INFO] 📊 修复进度: 已处理 45,000/55,000 条记录 (81.8%), 有contactinfo: 1,279, 无contactinfo: 43,721, 错误: 0, 耗时: 90.6秒
[23:18:10] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:12] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:13] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:15] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:16] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:16] [INFO] 📊 修复进度: 已处理 50,000/55,000 条记录 (90.9%), 有contactinfo: 1,398, 无contactinfo: 48,602, 错误: 0, 耗时: 98.4秒
[23:18:18] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:19] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:20] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:22] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:23] [INFO] ✅ 批量更新成功: 1000 条记录
[23:18:23] [INFO] 📊 修复进度: 已处理 55,000/55,000 条记录 (100.0%), 有contactinfo: 1,550, 无contactinfo: 53,450, 错误: 0, 耗时: 104.6秒
[23:18:23] [INFO] 
============================================================
[23:18:23] [INFO] 📈 Contactinfo 修复任务统计
[23:18:23] [INFO] ============================================================
[23:18:23] [INFO] 📊 需要修复的记录数: 55,000
[23:18:23] [INFO] 📊 实际处理记录数: 55,000
[23:18:23] [INFO] 📞 包含contactinfo的记录: 1,550
[23:18:23] [INFO] 📝 设置为空字符串的记录: 53,450
[23:18:23] [INFO] ❌ 处理失败的记录: 0
[23:18:23] [INFO] ⏱️ 总耗时: 104.62 秒
[23:18:23] [INFO] 🚀 平均处理速度: 525.7 记录/秒
[23:18:23] [INFO] 🎉 成功修复 55,000 条记录的contactinfo字段！
[23:18:23] [INFO] ✅ 修复成功率: 100.0%
