# Discogs 数据比较统一接口

这是一个简单的统一接口，用于获取不同类型的Discogs数据比较diff文件。

## 功能特点

- 🚀 **简单易用**: 一个命令获取任意类型的diff文件
- 📊 **支持四种数据类型**: release, master, artists, label
- 🔄 **智能缓存**: 自动检测已存在的diff文件，避免重复生成
- ⚡ **强制刷新**: 支持强制重新生成最新的diff文件
- 📈 **状态监控**: 查看所有diff文件的状态和信息

## 支持的数据类型

| 类型 | 描述 | 输出文件 |
|------|------|----------|
| `release` | Release数据比较 | `release_comparison_diff.csv` |
| `master` | Master数据比较 | `master_comparison_diff.csv` |
| `artists` | Artists数据比较 | `artists_comparison_diff.csv` |
| `label` | Label数据比较 | `label_comparison_diff.csv` |

## 使用方法

### 1. 命令行使用

```bash
# 获取release的diff文件
python get_diff.py release

# 获取master的diff文件  
python get_diff.py master

# 获取artists的diff文件
python get_diff.py artists

# 获取label的diff文件
python get_diff.py label

# 强制重新生成diff文件
python get_diff.py release --force

# 列出所有可用类型
python get_diff.py --list

# 检查所有diff文件状态
python get_diff.py --status
```

### 2. 作为Python模块使用

```python
from get_diff import DiffAPI

# 创建API实例
api = DiffAPI()

# 获取release的diff文件路径
diff_file = api.get_diff('release')
if diff_file:
    print(f"Diff文件路径: {diff_file}")
    # 可以进一步处理CSV文件
    import pandas as pd
    df = pd.read_csv(diff_file)
    print(f"共有 {len(df)} 条变化记录")

# 强制重新生成master的diff文件
diff_file = api.get_diff('master', force_regenerate=True)

# 检查所有diff文件状态
api.check_diff_status()

# 列出可用类型
api.list_available_types()
```

## 输出示例

### 获取diff文件
```bash
$ python get_diff.py release
找到现有的Release数据比较diff文件: /path/to/release_comparison_diff.csv

📁 Diff文件路径: /path/to/release_comparison_diff.csv
```

### 检查状态
```bash
$ python get_diff.py --status
Diff文件状态检查:
--------------------------------------------------
✅ release: 存在 (0.36MB, 修改时间: 2025-07-01 19:05:23)
✅ master: 存在 (3.02MB, 修改时间: 2025-06-29 18:01:51)
✅ artists: 存在 (12.44MB, 修改时间: 2025-06-29 17:28:51)
✅ label: 存在 (6.27MB, 修改时间: 2025-06-27 14:41:27)
```

### 强制重新生成
```bash
$ python get_diff.py release --force
正在生成Release数据比较diff文件...
✅ Release数据比较diff文件生成成功: /path/to/release_comparison_diff.csv

📁 Diff文件路径: /path/to/release_comparison_diff.csv
```

## 错误处理

- ❌ **不支持的数据类型**: 会显示支持的类型列表
- ❌ **脚本不存在**: 会提示比较脚本文件缺失
- ❌ **执行超时**: 超过2小时会自动终止
- ❌ **执行失败**: 会显示详细的错误信息

## 注意事项

1. **执行时间**: 根据数据量大小，生成diff文件可能需要几分钟到几小时
2. **内存使用**: 优化后的脚本内存使用控制在30MB以下
3. **文件位置**: diff文件会生成在对应的比较目录中
4. **权限要求**: 需要对目录有读写权限

## 技术实现

- 使用Python subprocess模块调用各个比较脚本
- 自动处理工作目录切换
- 支持超时控制和错误捕获
- 提供友好的进度提示和状态反馈
