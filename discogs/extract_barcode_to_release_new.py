#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从release表提取barcode数据并更新到release_new表
从searchRuleRelease.baseinfo.barcode字段提取barcode值，
并插入或更新到release_new表的barcode字段中
"""

import os
import json
import time
from pymongo import MongoClient
from datetime import datetime, timezone

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '1000'))  # 批处理大小
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))  # 最大处理记录数，0表示处理全部

# 输出文件路径
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'barcode_extraction_output.txt')

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    formatted_message = f"[{timestamp}] {message}"
    
    with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
        f.write(formatted_message + '\n')
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def extract_barcode_from_searchrule(searchrule_data):
    """从searchRuleRelease数据中提取barcode值"""
    try:
        if not searchrule_data:
            return None
        
        # 如果是字符串，尝试解析为JSON
        if isinstance(searchrule_data, str):
            try:
                searchrule_data = json.loads(searchrule_data)
            except json.JSONDecodeError:
                return None
        
        # 检查是否为字典
        if not isinstance(searchrule_data, dict):
            return None
        
        # 提取baseinfo.barcode
        baseinfo = searchrule_data.get('baseinfo', {})
        if isinstance(baseinfo, dict):
            barcode = baseinfo.get('barcode', '')
            # 返回非空的barcode值（包括以点开头的和全零的有效barcode）
            if barcode and barcode.strip():
                # 过滤掉明显无效的barcode（只有空格或特殊字符）
                cleaned_barcode = barcode.strip()
                if len(cleaned_barcode) > 0:
                    return cleaned_barcode
            return None
        
        return None
        
    except Exception as e:
        write_output(f"⚠️ 提取barcode时出错: {e}", False)
        return None

def process_barcode_extraction():
    """处理barcode提取和更新"""
    start_time = time.time()
    write_output("🚀 开始barcode提取和更新任务")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    write_output("✅ 已连接到MongoDB数据库")
    
    try:
        # 获取集合
        release_collection = db['release']
        release_new_collection = db['release_new']
        
        # 检查集合是否存在
        if 'release' not in db.list_collection_names():
            write_output("❌ 错误: release集合不存在")
            return
        
        # 确保release_new集合存在
        if 'release_new' not in db.list_collection_names():
            db.create_collection('release_new')
            write_output("✅ 创建了release_new集合")
        
        # 跳过耗时的统计操作，直接开始处理
        write_output("📊 跳过统计操作，直接开始处理数据...")
        
        # 处理统计
        processed_count = 0
        updated_count = 0
        skipped_count = 0
        error_count = 0
        barcode_found_count = 0
        
        write_output(f"🔄 开始处理，批处理大小: {BATCH_SIZE}")
        
        # 查询包含searchRuleRelease字段的记录
        query = {"searchRuleRelease": {"$exists": True}}
        projection = {"id": 1, "searchRuleRelease": 1}
        
        # 设置处理限制
        limit = MAX_RECORDS if MAX_RECORDS > 0 else None
        cursor = release_collection.find(query, projection)
        if limit:
            cursor = cursor.limit(limit)
            write_output(f"📝 限制处理记录数: {limit:,}")
        
        batch_updates = []
        
        for record in cursor:
            try:
                record_id = record.get('id')
                if not record_id:
                    skipped_count += 1
                    continue
                
                # 提取barcode
                searchrule_data = record.get('searchRuleRelease')
                barcode = extract_barcode_from_searchrule(searchrule_data)
                
                if barcode:
                    barcode_found_count += 1
                    
                    # 准备更新操作
                    update_doc = {
                        'barcode': barcode,
                        'updated_at': datetime.now(timezone.utc)
                    }
                    
                    batch_updates.append({
                        'filter': {'id': record_id},
                        'update': {'$set': update_doc},
                        'upsert': True
                    })
                
                processed_count += 1
                
                # 批量执行更新
                if len(batch_updates) >= BATCH_SIZE:
                    try:
                        # 执行批量更新
                        operations = []
                        for update_op in batch_updates:
                            from pymongo import UpdateOne
                            operations.append(UpdateOne(
                                update_op['filter'],
                                update_op['update'],
                                upsert=update_op['upsert']
                            ))
                        
                        result = release_new_collection.bulk_write(operations)
                        updated_count += result.upserted_count + result.modified_count
                        
                        write_output(f"📈 已处理 {processed_count:,} 条记录，更新 {updated_count:,} 条，发现barcode {barcode_found_count:,} 个")
                        
                    except Exception as e:
                        write_output(f"❌ 批量更新失败: {e}")
                        error_count += len(batch_updates)
                    
                    batch_updates = []
                
                # 显示进度
                if processed_count % 10000 == 0:
                    write_output(f"🔄 进度: 已处理 {processed_count:,} 条记录...")
                
            except Exception as e:
                write_output(f"❌ 处理记录 {record.get('id', 'unknown')} 时出错: {e}", False)
                error_count += 1
                continue
        
        # 处理剩余的批量更新
        if batch_updates:
            try:
                operations = []
                for update_op in batch_updates:
                    from pymongo import UpdateOne
                    operations.append(UpdateOne(
                        update_op['filter'],
                        update_op['update'],
                        upsert=update_op['upsert']
                    ))
                
                result = release_new_collection.bulk_write(operations)
                updated_count += result.upserted_count + result.modified_count
                
            except Exception as e:
                write_output(f"❌ 最终批量更新失败: {e}")
                error_count += len(batch_updates)
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 输出最终统计
        write_output("\n" + "="*60)
        write_output("📈 Barcode提取任务完成统计")
        write_output("="*60)
        write_output(f"✅ 总处理记录数: {processed_count:,}")
        write_output(f"🎯 发现barcode记录数: {barcode_found_count:,}")
        write_output(f"📝 成功更新记录数: {updated_count:,}")
        write_output(f"⏭️ 跳过记录数: {skipped_count:,}")
        write_output(f"❌ 错误记录数: {error_count:,}")
        write_output(f"📊 Barcode发现率: {(barcode_found_count/processed_count*100):.2f}%" if processed_count > 0 else "0%")
        write_output(f"⏱️ 总耗时: {processing_time:.2f} 秒")
        write_output(f"⚡ 平均处理速度: {processed_count/processing_time:.1f} 记录/秒" if processing_time > 0 else "N/A")
        write_output("="*60)
        
    except Exception as e:
        write_output(f"❌ 处理过程中出错: {e}")
        raise
    finally:
        # 关闭数据库连接
        client.close()
        write_output("🔒 已关闭MongoDB连接")
        write_output(f"\n📄 详细日志已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    try:
        process_barcode_extraction()
        print(f"\n✅ Barcode提取任务完成！详细日志请查看: {OUTPUT_FILE}")
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        exit(1)
