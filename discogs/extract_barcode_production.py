#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
生产版本的barcode提取脚本
从release表的searchRuleRelease.baseinfo.barcode字段提取barcode值，
并插入或更新到release_new表的barcode字段中

使用方法:
1. 测试运行: MAX_RECORDS=1000 python extract_barcode_production.py
2. 生产运行: python extract_barcode_production.py
3. 自定义批处理: BATCH_SIZE=10000 python extract_barcode_production.py

环境变量:
- MONGO_URI: MongoDB连接字符串
- DB_NAME: 数据库名称
- BATCH_SIZE: 批处理大小 (默认: 5000)
- MAX_RECORDS: 最大处理记录数，0表示处理全部 (默认: 0)
- OUTPUT_FILE: 输出日志文件路径 (默认: barcode_extraction_production.txt)
"""

import os
import sys
import time
import signal
from pymongo import MongoClient, UpdateOne
from datetime import datetime, timezone

# 配置参数
MONGO_URI = os.getenv('MONGO_URI', '**********************************************************')
DB_NAME = os.getenv('DB_NAME', 'music_test')
BATCH_SIZE = int(os.getenv('BATCH_SIZE', '5000'))
MAX_RECORDS = int(os.getenv('MAX_RECORDS', '0'))
OUTPUT_FILE = os.getenv('OUTPUT_FILE', 'barcode_extraction_production.txt')

# 全局变量用于优雅关闭
shutdown_requested = False
processed_count = 0

def signal_handler(signum, frame):
    """处理中断信号"""
    global shutdown_requested
    shutdown_requested = True
    write_output(f"⚠️ 接收到中断信号 {signum}，正在优雅关闭...")

# 注册信号处理器
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# 确保输出文件不存在
if os.path.exists(OUTPUT_FILE):
    os.remove(OUTPUT_FILE)

def write_output(message, print_to_console=True):
    """将消息写入输出文件，并可选择是否打印到控制台"""
    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    formatted_message = f"[{timestamp}] {message}"
    
    try:
        with open(OUTPUT_FILE, 'a', encoding='utf-8') as f:
            f.write(formatted_message + '\n')
    except Exception as e:
        print(f"写入日志文件失败: {e}")
    
    if print_to_console:
        print(formatted_message)

def connect_to_mongodb():
    """连接到MongoDB并返回数据库对象"""
    try:
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=30000)
        # 测试连接
        client.admin.command('ping')
        return client, client[DB_NAME]
    except Exception as e:
        write_output(f"❌ MongoDB连接失败: {e}")
        raise

def create_indexes(release_new_collection):
    """为release_new集合创建必要的索引"""
    try:
        # 为id字段创建索引（如果不存在）
        release_new_collection.create_index("id", background=True)
        write_output("✅ 已确保id字段索引存在")
        
        # 为barcode字段创建索引（如果不存在）
        release_new_collection.create_index("barcode", background=True)
        write_output("✅ 已确保barcode字段索引存在")
        
    except Exception as e:
        write_output(f"⚠️ 创建索引时出错: {e}")

def process_barcode_extraction():
    """主要的barcode提取和更新处理函数"""
    global processed_count, shutdown_requested
    
    start_time = time.time()
    write_output("🚀 开始生产版本barcode提取和更新任务")
    write_output(f"📋 配置参数:")
    write_output(f"   - 批处理大小: {BATCH_SIZE:,}")
    write_output(f"   - 最大记录数: {'全部' if MAX_RECORDS == 0 else f'{MAX_RECORDS:,}'}")
    write_output(f"   - 输出文件: {OUTPUT_FILE}")
    
    # 连接MongoDB
    client, db = connect_to_mongodb()
    write_output("✅ 已连接到MongoDB数据库")
    
    try:
        # 获取集合
        release_collection = db['release']
        release_new_collection = db['release_new']
        
        # 检查集合是否存在
        if 'release' not in db.list_collection_names():
            write_output("❌ 错误: release集合不存在")
            return False
        
        # 确保release_new集合存在
        if 'release_new' not in db.list_collection_names():
            db.create_collection('release_new')
            write_output("✅ 创建了release_new集合")
        
        # 创建索引
        create_indexes(release_new_collection)
        
        write_output("🔍 使用聚合管道查找有效barcode记录...")
        
        # 构建聚合管道
        pipeline = [
            # 匹配包含searchRuleRelease字段的记录
            {"$match": {"searchRuleRelease": {"$exists": True}}},
            
            # 投影并提取barcode
            {"$project": {
                "id": 1,
                "barcode": {
                    "$cond": {
                        "if": {"$and": [
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", None]},
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", ""]},
                            {"$ne": ["$searchRuleRelease.baseinfo.barcode", " "]}
                        ]},
                        "then": "$searchRuleRelease.baseinfo.barcode",
                        "else": None
                    }
                }
            }},
            
            # 只保留有效barcode的记录
            {"$match": {"barcode": {"$ne": None}}},
            
            # 限制记录数（如果设置了MAX_RECORDS）
            *([{"$limit": MAX_RECORDS}] if MAX_RECORDS > 0 else [])
        ]
        
        write_output(f"🔄 开始处理，批处理大小: {BATCH_SIZE:,}")
        if MAX_RECORDS > 0:
            write_output(f"📝 限制处理记录数: {MAX_RECORDS:,}")
        
        # 处理统计
        processed_count = 0
        updated_count = 0
        error_count = 0
        batch_updates = []
        
        # 执行聚合查询
        cursor = release_collection.aggregate(pipeline, allowDiskUse=True, batchSize=1000)
        
        write_output("📊 开始处理记录...")
        
        for record in cursor:
            # 检查是否需要停止
            if shutdown_requested:
                write_output("⚠️ 检测到停止请求，正在保存当前批次...")
                break
            
            try:
                record_id = record.get('id')
                barcode = record.get('barcode')
                
                if not record_id or not barcode:
                    continue
                
                # 准备更新操作
                update_doc = {
                    'barcode': barcode,
                    'updated_at': datetime.now(timezone.utc)
                }
                
                batch_updates.append(UpdateOne(
                    {'id': record_id},
                    {'$set': update_doc},
                    upsert=True
                ))
                
                processed_count += 1
                
                # 批量执行更新
                if len(batch_updates) >= BATCH_SIZE:
                    try:
                        result = release_new_collection.bulk_write(batch_updates, ordered=False)
                        updated_count += result.upserted_count + result.modified_count
                        
                        elapsed_time = time.time() - start_time
                        speed = processed_count / elapsed_time if elapsed_time > 0 else 0
                        
                        write_output(f"📈 已处理 {processed_count:,} 条记录，更新 {updated_count:,} 条 "
                                   f"(速度: {speed:.1f} 记录/秒)")
                        
                    except Exception as e:
                        write_output(f"❌ 批量更新失败: {e}")
                        error_count += len(batch_updates)
                    
                    batch_updates = []
                
                # 显示进度
                if processed_count % 10000 == 0:
                    elapsed_time = time.time() - start_time
                    speed = processed_count / elapsed_time if elapsed_time > 0 else 0
                    write_output(f"🔄 进度: 已处理 {processed_count:,} 条记录 "
                               f"(速度: {speed:.1f} 记录/秒, 耗时: {elapsed_time:.1f}秒)")
                
            except Exception as e:
                write_output(f"❌ 处理记录时出错: {e}", False)
                error_count += 1
                continue
        
        # 处理剩余的批量更新
        if batch_updates:
            try:
                result = release_new_collection.bulk_write(batch_updates, ordered=False)
                updated_count += result.upserted_count + result.modified_count
                write_output(f"📈 最终批次: 更新 {result.upserted_count + result.modified_count} 条记录")
                
            except Exception as e:
                write_output(f"❌ 最终批量更新失败: {e}")
                error_count += len(batch_updates)
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 输出最终统计
        write_output("\n" + "="*70)
        write_output("📈 生产版本Barcode提取任务完成统计")
        write_output("="*70)
        write_output(f"✅ 总处理记录数: {processed_count:,}")
        write_output(f"📝 成功更新记录数: {updated_count:,}")
        write_output(f"❌ 错误记录数: {error_count:,}")
        write_output(f"📊 成功率: {(updated_count/processed_count*100):.2f}%" if processed_count > 0 else "0%")
        write_output(f"⏱️ 总耗时: {processing_time:.2f} 秒 ({processing_time/60:.1f} 分钟)")
        write_output(f"⚡ 平均处理速度: {processed_count/processing_time:.1f} 记录/秒" if processing_time > 0 else "N/A")
        write_output("="*70)
        
        # 验证结果
        if updated_count > 0:
            write_output("\n🔍 验证更新结果...")
            
            try:
                # 统计release_new中的barcode记录数
                total_barcode_count = release_new_collection.count_documents({"barcode": {"$exists": True}})
                write_output(f"📊 release_new表中总barcode记录数: {total_barcode_count:,}")
                
                # 查询样本数据
                sample_results = list(release_new_collection.find(
                    {"barcode": {"$exists": True}},
                    {"id": 1, "barcode": 1, "updated_at": 1}
                ).limit(3))
                
                if sample_results:
                    write_output("✅ release_new表中的样本数据:")
                    for i, result in enumerate(sample_results, 1):
                        write_output(f"   {i}. ID: {result.get('id')}, barcode: '{result.get('barcode')}'")
                
            except Exception as e:
                write_output(f"⚠️ 验证过程中出错: {e}")
        
        return True
        
    except Exception as e:
        write_output(f"❌ 处理过程中出错: {e}")
        return False
    finally:
        # 关闭数据库连接
        client.close()
        write_output("🔒 已关闭MongoDB连接")
        write_output(f"\n📄 详细日志已保存到: {OUTPUT_FILE}")

if __name__ == "__main__":
    try:
        success = process_barcode_extraction()
        
        if success:
            print(f"\n✅ 生产版本Barcode提取任务完成！")
            print(f"📊 总共处理了 {processed_count:,} 条记录")
            print(f"📄 详细日志请查看: {OUTPUT_FILE}")
            sys.exit(0)
        else:
            print(f"\n❌ 任务执行失败！详细信息请查看: {OUTPUT_FILE}")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print(f"\n⚠️ 任务被用户中断！已处理 {processed_count:,} 条记录")
        print(f"📄 详细日志请查看: {OUTPUT_FILE}")
        sys.exit(130)
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        print(f"📄 详细日志请查看: {OUTPUT_FILE}")
        sys.exit(1)
