# Discogs 数据比较 Web API 服务

这是一个基于Flask的Web API服务，提供HTTP接口来获取不同类型的Discogs数据比较diff文件。

## 🚀 快速开始

### 1. 安装依赖

```bash
cd discogs
pip3 install -r requirements.txt
```

### 2. 启动服务

```bash
# 方式1: 使用启动脚本
./start_api.sh

# 方式2: 直接运行
python3 api_server.py
```

服务将在 `http://localhost:8080` 启动

### 3. 验证服务

```bash
# 健康检查
curl http://localhost:8080/api/health

# 查看API文档
curl http://localhost:8080/
```

## 📖 API 接口文档

### 基础信息

- **服务地址**: `http://localhost:8080`
- **数据格式**: JSON
- **支持的数据类型**: `release`, `master`, `artists`, `label`

### 接口列表

#### 1. 首页 - API文档
```
GET /
```
返回完整的API文档和使用说明。

#### 2. 健康检查
```
GET /api/health
```
检查服务状态。

**响应示例**:
```json
{
  "status": "healthy",
  "timestamp": "2025-07-01T19:25:28.924960",
  "service": "Discogs Diff API"
}
```

#### 3. 获取支持的数据类型
```
GET /api/types
```
获取所有支持的数据类型列表。

**响应示例**:
```json
{
  "supported_types": [
    {
      "type": "release",
      "description": "Release数据比较",
      "output_file": "release_comparison_diff.csv"
    }
  ],
  "total_count": 4
}
```

#### 4. 查看所有diff文件状态
```
GET /api/status
```
查看所有类型diff文件的存在状态、大小、修改时间等信息。

**响应示例**:
```json
{
  "diff_files": [
    {
      "type": "release",
      "description": "Release数据比较",
      "exists": true,
      "file_path": "/path/to/release_comparison_diff.csv",
      "size_mb": 0.36,
      "modified_time": "2025-07-01T19:05:23.410283",
      "age_hours": 0.3
    }
  ],
  "total_count": 4,
  "existing_count": 4
}
```

#### 5. 获取指定类型的diff文件
```
GET /api/diff/<type>
GET /api/diff/<type>?force=true
```

**路径参数**:
- `type`: 数据类型 (`release`, `master`, `artists`, `label`)

**查询参数**:
- `force`: 是否强制重新生成 (`true`/`false`, 默认: `false`)

**响应示例**:
```json
{
  "success": true,
  "data_type": "release",
  "description": "Release数据比较",
  "file_path": "/path/to/release_comparison_diff.csv",
  "file_name": "release_comparison_diff.csv",
  "size_mb": 0.36,
  "modified_time": "2025-07-01T19:05:23.410283",
  "download_url": "/api/diff/release/download",
  "force_regenerated": false
}
```

#### 6. 下载diff文件
```
GET /api/diff/<type>/download
```
直接下载指定类型的CSV diff文件。

## 🎯 使用示例

### 命令行示例

```bash
# 1. 检查服务状态
curl http://localhost:8080/api/health

# 2. 查看支持的类型
curl http://localhost:8080/api/types

# 3. 查看所有diff文件状态
curl http://localhost:8080/api/status

# 4. 获取release的diff文件信息
curl http://localhost:8080/api/diff/release

# 5. 强制重新生成master的diff文件
curl "http://localhost:8080/api/diff/master?force=true"

# 6. 下载artists的diff文件
curl -O http://localhost:8080/api/diff/artists/download
```

### JavaScript示例

```javascript
// 获取release的diff文件信息
fetch('http://localhost:8080/api/diff/release')
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      console.log('文件路径:', data.file_path);
      console.log('文件大小:', data.size_mb, 'MB');
      console.log('下载链接:', data.download_url);
    }
  });

// 强制重新生成并获取master的diff文件
fetch('http://localhost:8080/api/diff/master?force=true')
  .then(response => response.json())
  .then(data => console.log(data));

// 下载文件
window.open('http://localhost:8080/api/diff/release/download');
```

### Python示例

```python
import requests

# 获取diff文件信息
response = requests.get('http://localhost:8080/api/diff/release')
data = response.json()

if data.get('success'):
    print(f"文件路径: {data['file_path']}")
    print(f"文件大小: {data['size_mb']} MB")
    
    # 下载文件
    download_response = requests.get(f"http://localhost:8080{data['download_url']}")
    with open('release_diff.csv', 'wb') as f:
        f.write(download_response.content)
```

## 🔧 部署配置

### 开发环境
```bash
python3 api_server.py
```

### 生产环境
建议使用WSGI服务器如Gunicorn：

```bash
# 安装Gunicorn
pip3 install gunicorn

# 启动服务
gunicorn -w 4 -b 0.0.0.0:8080 api_server:app
```

### Docker部署
```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8080

CMD ["gunicorn", "-w", "4", "-b", "0.0.0.0:8080", "api_server:app"]
```

## ⚠️ 注意事项

1. **处理时间**: 根据数据量大小，生成diff文件可能需要几分钟到几小时
2. **内存使用**: 优化后的脚本内存使用控制在30MB以下
3. **文件权限**: 确保服务有读写权限访问比较脚本和输出目录
4. **端口配置**: 默认使用8080端口，可在代码中修改
5. **CORS支持**: 已内置简单的CORS支持，支持跨域访问

## 🐛 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口使用情况
   lsof -i :8080
   # 修改api_server.py中的端口号
   ```

2. **依赖缺失**
   ```bash
   pip3 install flask
   ```

3. **权限问题**
   ```bash
   chmod +x start_api.sh
   ```

4. **比较脚本不存在**
   - 确保各个比较目录下的脚本文件存在
   - 检查文件路径配置是否正确

## 📊 性能监控

API服务提供了详细的状态信息，包括：
- 文件存在状态
- 文件大小
- 最后修改时间
- 文件年龄（小时）

可以通过 `/api/status` 接口监控所有diff文件的状态。
