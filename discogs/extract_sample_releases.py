#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Discogs XML样本提取器
从releases.xml.gz压缩文件中提取前50,000条记录并保存为新的XML文件
保持原始XML格式和结构，使用内存高效的流式处理
"""

import gzip
import time
import os
import sys
import glob

# 配置参数
MAX_RECORDS = 50000  # 提取的最大记录数
OUTPUT_FILE = 'releases_sample_50k.xml'  # 输出文件名
LOG_FILE = 'extract_sample_output.txt'  # 日志文件

def find_releases_file():
    """
    查找releases数据文件
    优先查找data目录，然后查找deploy/windows/data目录
    
    Returns:
        找到的文件路径，如果没找到返回None
    """
    # 搜索路径列表
    search_paths = [
        'data/*releases*.gz',
        'data/*releases*.zip', 
        'deploy/windows/data/*releases*.gz',
        'deploy/windows/data/*releases*.zip',
        'release/*releases*.gz',
        'release/*releases*.zip'
    ]
    
    for pattern in search_paths:
        found_files = glob.glob(pattern)
        if found_files:
            # 如果找到多个文件，选择最新的（按文件名排序）
            found_files.sort()
            selected_file = found_files[-1]
            print(f"✅ 检测到releases文件: {selected_file}")
            return selected_file
    
    print("❌ 未找到releases数据文件")
    print("   搜索的路径包括:")
    for pattern in search_paths:
        print(f"   - {pattern}")
    return None

def write_output(message, print_to_console=True):
    """将消息写入日志文件，并可选择是否打印到控制台"""
    with open(LOG_FILE, 'a', encoding='utf-8') as f:
        f.write(message + '\n')
    
    if print_to_console:
        print(message)

def create_xml_output_file(output_path):
    """
    创建输出XML文件并写入头部
    
    Args:
        output_path: 输出文件路径
        
    Returns:
        文件句柄
    """
    try:
        file_handle = open(output_path, 'w', encoding='utf-8')
        # 写入XML头部和根元素开始标签
        file_handle.write('<?xml version="1.0" encoding="UTF-8"?>\n')
        file_handle.write('<releases>\n')
        return file_handle
    except Exception as e:
        write_output(f"❌ 创建输出文件失败: {e}")
        return None

def close_xml_output_file(file_handle):
    """
    关闭XML文件并写入结束标签
    
    Args:
        file_handle: 文件句柄
    """
    try:
        if file_handle:
            file_handle.write('</releases>\n')
            file_handle.close()
    except Exception as e:
        write_output(f"❌ 关闭输出文件时出错: {e}")

def extract_sample_releases():
    """主处理函数：提取样本releases记录"""
    start_time = time.time()
    
    # 清空日志文件
    if os.path.exists(LOG_FILE):
        os.remove(LOG_FILE)
    
    write_output("="*60)
    write_output("Discogs XML样本提取器")
    write_output("="*60)
    write_output(f"目标提取记录数: {MAX_RECORDS:,}")
    write_output(f"输出文件: {OUTPUT_FILE}")
    
    # 查找数据文件
    xml_file = find_releases_file()
    if not xml_file:
        write_output("❌ 错误: 无法找到releases数据文件")
        sys.exit(1)
    
    # 检查文件大小
    try:
        file_size = os.path.getsize(xml_file)
        write_output(f"📁 数据文件大小: {file_size / (1024*1024*1024):.2f} GB")
    except Exception as e:
        write_output(f"⚠️  无法获取文件大小: {e}")
    
    # 创建输出文件
    output_handle = create_xml_output_file(OUTPUT_FILE)
    if not output_handle:
        sys.exit(1)
    
    processed_count = 0
    total_records_found = 0
    
    try:
        write_output(f"🚀 开始处理XML文件: {xml_file}")
        write_output("📊 进度显示: 每处理1,000条记录显示一次进度")
        
        # 打开gzip文件并逐行读取
        with gzip.open(xml_file, 'rt', encoding='utf-8') as f:
            buffer = ""
            in_release = False
            
            for line in f:
                if '<release ' in line:
                    buffer = line
                    in_release = True
                    total_records_found += 1
                elif '</release>' in line and in_release:
                    buffer += line
                    in_release = False
                    
                    # 将完整的release记录写入输出文件
                    output_handle.write(buffer)
                    processed_count += 1
                    
                    # 显示进度
                    if processed_count % 1000 == 0:
                        progress_msg = f"📈 已提取 {processed_count:,} 条记录..."
                        write_output(progress_msg)
                    
                    # 达到最大记录数时退出
                    if processed_count >= MAX_RECORDS:
                        write_output(f"✅ 已达到目标记录数 ({MAX_RECORDS:,})，停止处理")
                        break
                    
                    # 清空缓冲区
                    buffer = ""
                elif in_release:
                    buffer += line
                
                # 如果已达到最大记录数，跳出外层循环
                if processed_count >= MAX_RECORDS:
                    break
                    
    except Exception as e:
        error_msg = f"❌ 处理过程中出错: {e}"
        write_output(error_msg)
        return False
    finally:
        # 关闭输出文件
        close_xml_output_file(output_handle)
        
        # 计算处理时间
        processing_time = time.time() - start_time
        
        # 输出处理结果统计
        stats = [
            "\n" + "="*60,
            "📊 处理结果统计",
            "="*60,
            f"✅ 成功提取了 {processed_count:,} 条记录",
            f"📄 XML文件中共发现 {total_records_found:,} 条记录",
            f"⏱️  处理时长: {processing_time:.2f} 秒",
            (f"⚡ 平均每条记录处理时间: {processing_time/processed_count:.4f} 秒"
             if processed_count > 0 else "⚡ 平均处理时间: 0 秒"),
            f"💾 输出文件: {OUTPUT_FILE}",
            "="*60
        ]
        
        # 打印到控制台和写入文件
        for stat in stats:
            write_output(stat)
        
        write_output(f"\n📝 详细日志已保存到: {LOG_FILE}")
        
        # 检查输出文件大小
        try:
            if os.path.exists(OUTPUT_FILE):
                output_size = os.path.getsize(OUTPUT_FILE)
                write_output(f"📁 输出文件大小: {output_size / (1024*1024):.2f} MB")
        except Exception as e:
            write_output(f"⚠️  无法获取输出文件大小: {e}")
    
    return processed_count > 0

if __name__ == "__main__":
    success = extract_sample_releases()
    if success:
        print(f"\n🎉 样本提取完成！输出文件: {OUTPUT_FILE}")
    else:
        print("\n❌ 样本提取失败，请查看日志文件了解详情")
        sys.exit(1)
